name: Azure Static Web Apps CI/CD
pr:
  branches:
    include:
      - main
trigger:
  branches:
    include:
      - main
jobs:
- job: build_and_deploy_job
  displayName: Build and Deploy Job
  condition: or(eq(variables['Build.Reason'], 'Manual'),or(eq(variables['Build.Reason'], 'PullRequest'),eq(variables['Build.Reason'], 'IndividualCI')))
  pool:
    vmImage: ubuntu-latest
  variables:
  - group: Azure-Static-Web-Apps-ambitious-river-0f271f61e-variable-group
  steps:
  - checkout: self
    submodules: true
  - task: AzureStaticWebApp@0
    inputs:
      azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_AMBITIOUS_RIVER_0F271F61E)
      app_location: "/"
      api_location: ""
      output_location: "dist"  # Changed from "build" to "dist"
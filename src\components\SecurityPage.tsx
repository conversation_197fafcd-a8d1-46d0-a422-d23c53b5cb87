import React from 'react';
import { Shield, Lock, Eye, Users, Server, CheckCircle } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Navbar from './Navbar';
import Footer from './Footer';

const SecurityPage = () => {
    return (
        <div className="min-h-screen bg-white">
            <Navbar />
            {/* Hero Section */}
            <section className="pt-32 pb-20 bg-gradient-to-br from-slate-50 to-blue-50">
                <div className="container mx-auto px-4">
                    <div className="text-center max-w-4xl mx-auto">
                        <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-800">
                            We know that security is paramount
                        </h1>
                        <p className="text-xl text-gray-600 mb-8">
                            We are committed to protecting your data and privacy
                        </p>

                    </div>
                </div>
            </section>

            {/* Main Security Features */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
                        {/* Protecting Your Data */}
                        <Card className="p-8">
                            <CardContent className="p-0">
                                <div className="flex items-center mb-4">
                                    <Lock className="h-8 w-8 text-blue-500 mr-3" />
                                    <h2 className="text-2xl font-bold text-gray-800">Protecting your Data</h2>
                                </div>
                                <p className="text-gray-600 leading-relaxed">
                                    FINXPLORER reads and saves your banking and investing data to build a comprehensive financial dashboard.
                                    <strong className="text-gray-800"> FINXPLORER is strictly read-only</strong>. Trading, fund withdrawals,
                                    money transfers or other transactions are not possible through the platform to keep your money safe.
                                </p>
                            </CardContent>
                        </Card>

                        {/* Safe and Secure Technology */}
                        <Card className="p-8">
                            <CardContent className="p-0">
                                <div className="flex items-center mb-4">
                                    <Server className="h-8 w-8 text-blue-500 mr-3" />
                                    <h2 className="text-2xl font-bold text-gray-800">Safe and Secure Technology</h2>
                                </div>
                                <p className="text-gray-600 leading-relaxed">
                                    We use the latest technology and have strict processes in place to protect your financial information.
                                    We use cloud services provided by Microsoft Azure and managed by
                                    <strong className="text-gray-800"> a team with extensive experience</strong> in secure web hosting.

                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Security Pillars */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {/* Bank-Level Security */}
                        <Card className="p-6 text-center hover:shadow-lg transition-shadow">
                            <CardContent className="p-0">
                                <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <Shield className="h-8 w-8 text-blue-600" />
                                </div>
                                <h3 className="text-xl font-bold mb-3 text-gray-800">Bank-Level Security</h3>
                                <p className="text-gray-600 text-sm leading-relaxed">
                                    We secure your data with <strong>AES-256 encryption</strong>, the same rigorous standards used by banks.
                                    We also encrypt all in-transit data using the latest <strong>TLS 1.3 technology</strong>.
                                    The credentials you entrust us are encrypted, and <strong>none of our employees can access</strong> them.
                                </p>
                            </CardContent>
                        </Card>

                        {/* Secure Authentication */}
                        <Card className="p-6 text-center hover:shadow-lg transition-shadow">
                            <CardContent className="p-0">
                                <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <Lock className="h-8 w-8 text-green-600" />
                                </div>
                                <h3 className="text-xl font-bold mb-3 text-gray-800">Secure Authentication</h3>
                                <p className="text-gray-600 text-sm leading-relaxed">
                                    We support <strong>two-factor authentication</strong> in your FINXPLORER account for enhanced security.
                                    When enabled, a security PIN is sent to your smartphone or authentication app to authorize login from a new device.
                                </p>
                            </CardContent>
                        </Card>

                        {/* Privacy Protection */}
                        <Card className="p-6 text-center hover:shadow-lg transition-shadow">
                            <CardContent className="p-0">
                                <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <Eye className="h-8 w-8 text-purple-600" />
                                </div>
                                <h3 className="text-xl font-bold mb-3 text-gray-800">No one will see your data</h3>
                                <p className="text-gray-600 text-sm leading-relaxed">
                                    No one on the FINXPLORER team will see your private data unless you specifically choose to share data with support.
                                    Private mode prevents <strong>over-shoulder snooping</strong> by hiding all numbers and showing only percentage changes.
                                </p>
                            </CardContent>
                        </Card>

                        {/* Privacy Commitment */}
                        <Card className="p-6 text-center hover:shadow-lg transition-shadow">
                            <CardContent className="p-0">
                                <div className="bg-pink-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <Users className="h-8 w-8 text-pink-600" />
                                </div>
                                <h3 className="text-xl font-bold mb-3 text-gray-800">Committed to your privacy</h3>
                                <p className="text-gray-600 text-sm leading-relaxed">
                                    FINXPLORER will never sell, publish or share any identifiable personal information to third parties.
                                    We maintain compliance with privacy laws like CCPA, PIPEDA and GDPR.
                                </p>
                            </CardContent>
                        </Card>

                        {/* Continuous Monitoring */}
                        <Card className="p-6 text-center hover:shadow-lg transition-shadow">
                            <CardContent className="p-0">
                                <div className="bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <CheckCircle className="h-8 w-8 text-orange-600" />
                                </div>
                                <h3 className="text-xl font-bold mb-3 text-gray-800">Constant Security Testing</h3>
                                <p className="text-gray-600 text-sm leading-relaxed">
                                    We monitor continuously and run daily vulnerability scans on our website and apps to ensure
                                    <strong> continuous security</strong>. Our security team is always vigilant.
                                </p>
                            </CardContent>
                        </Card>

                        {/* Control in Your Hands */}
                        <Card className="p-6 text-center hover:shadow-lg transition-shadow">
                            <CardContent className="p-0">
                                <div className="bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <Shield className="h-8 w-8 text-indigo-600" />
                                </div>
                                <h3 className="text-xl font-bold mb-3 text-gray-800">Control in your hands</h3>
                                <p className="text-gray-600 text-sm leading-relaxed">
                                    You can easily review new transactions across all your accounts. Choose to receive daily transaction reviews
                                    to <strong>detect any suspicious activity</strong>. Delete your account in one click - it's unrecoverable.
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>

            {/* How We Collect Data */}
            <section className="py-16 bg-slate-50">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
                            How do we collect your financial data?
                        </h2>
                        <Card className="p-8 mb-8">
                            <CardContent className="p-0">
                                <p className="text-gray-600 leading-relaxed mb-6">
                                    FINXPLORER is a strictly read-only platform. You cannot trade, withdraw funds, transfer money or conduct
                                    any other transactions through it. This is to keep your money safe. We establish a secure connection between
                                    FINXPLORER and your financial institution using industry-standard protocols.
                                </p>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div className="text-center">
                                        <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                            <Lock className="h-6 w-6 text-blue-600" />
                                        </div>
                                        <h4 className="font-semibold text-gray-800 mb-2">API Authentication</h4>
                                        <p className="text-sm text-gray-600">
                                            When available, we favor API authentication as the most secure method
                                        </p>
                                    </div>
                                    <div className="text-center">
                                        <div className="bg-green-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                            <Shield className="h-6 w-6 text-green-600" />
                                        </div>
                                        <h4 className="font-semibold text-gray-800 mb-2">Encrypted Credentials</h4>
                                        <p className="text-sm text-gray-600">
                                            All credentials are encrypted and never accessible to our team
                                        </p>
                                    </div>
                                    <div className="text-center">
                                        <div className="bg-purple-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                                            <CheckCircle className="h-6 w-6 text-purple-600" />
                                        </div>
                                        <h4 className="font-semibold text-gray-800 mb-2">Read-Only Access</h4>
                                        <p className="text-sm text-gray-600">
                                            We only read your data - no transactions are possible
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>

            {/* Security FAQ */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">Security FAQ</h2>
                        <div className="space-y-6">
                            <Card className="p-6">
                                <CardContent className="p-0">
                                    <h3 className="text-lg font-semibold mb-3 text-gray-800">
                                        Why do you need my banking credentials?
                                    </h3>
                                    <p className="text-gray-600">
                                        Your credentials are needed to establish a secure connection between FINXPLORER and your financial institution.
                                        This allows us to collect your financial data and build your comprehensive dashboard and reports.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="p-6">
                                <CardContent className="p-0">
                                    <h3 className="text-lg font-semibold mb-3 text-gray-800">
                                        Do you sell my personal information?
                                    </h3>
                                    <p className="text-gray-600">
                                        Your security and the protection of your personal information is our priority. We will never sell,
                                        publish or share your contact information or any identifiable personal information to third parties.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="p-6">
                                <CardContent className="p-0">
                                    <h3 className="text-lg font-semibold mb-3 text-gray-800">
                                        What is 2FA and should I use it?
                                    </h3>
                                    <p className="text-gray-600">
                                        FINXPLORER supports two-factor verification (2FA) and is strongly recommended to protect your account.
                                        When configured, a unique temporary code will be required with your password to login to FINXPLORER.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="p-6">
                                <CardContent className="p-0">
                                    <h3 className="text-lg font-semibold mb-3 text-gray-800">
                                        How do you protect my personal information?
                                    </h3>
                                    <p className="text-gray-600">
                                        FINXPLORER maintains compliance with major privacy regulations including CCPA, PIPEDA, and GDPR.
                                        We have documented policies to support our commitment towards data privacy and information security.
                                    </p>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </section>

            {/* Contact Security Team */}
            <section className="py-16 bg-blue-50">
                <div className="container mx-auto px-4">
                    <div className="text-center max-w-2xl mx-auto">
                        <h2 className="text-3xl font-bold mb-6 text-gray-800">
                            Report Security Issues or Questions
                        </h2>
                        <p className="text-gray-600 mb-8">
                            You can get in touch with our security team to report any issues, concerns or ask security related questions.
                        </p>
                        <Button
                            className="btn-lepton-blue px-8 py-3"
                            onClick={() => window.location.href = 'mailto:<EMAIL>?subject=Security Inquiry&body=Hello FINXPLORER Security Team,%0D%0A%0D%0AI have a security-related question or concern:%0D%0A%0D%0A'}
                        >
                            Contact Security Team
                        </Button>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-16 bg-gray-900 text-white">
                <div className="container mx-auto px-4">
                    <div className="text-center max-w-2xl mx-auto">
                        <h2 className="text-3xl font-bold mb-6">Get started with FINXPLORER today!</h2>
                        <p className="text-gray-300 mb-8">
                            Join the thousands of people using FINXPLORER to manage their financial data securely!
                        </p>
                        <Button className="btn-lepton-pink px-8 py-3 text-lg">
                            Try the demo
                        </Button>
                    </div>
                </div>
            </section>
            <Footer />
        </div>
    );
};

export default SecurityPage; 
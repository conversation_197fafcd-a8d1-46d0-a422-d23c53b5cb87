import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>hart, Shield, Search, Calendar, Users, ChevronDown, Eye } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const FeaturesSection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [showComingSoon, setShowComingSoon] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Unobserve after animation starts
          if (sectionRef.current) {
            observer.unobserve(sectionRef.current);
          }
        }
      },
      { threshold: 0.2 } // Trigger when 20% of the element is visible
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  const features = [
    {
      icon: (
        <span className="relative inline-block">
          {/* Pink hand-drawn accent: sparkles */}
          <svg className="absolute -top-3 -left-3 w-7 h-7" viewBox="0 0 28 28" fill="none">
            <path d="M14 2v4M14 22v4M2 14h4M22 14h4M5.5 5.5l2.5 2.5M20 20l2.5 2.5M5.5 22.5l2.5-2.5M20 8l2.5-2.5" stroke="#EC4899" strokeWidth="2" strokeLinecap="round" />
          </svg>
          <Shield className="h-10 w-10 text-blue-500 relative z-10" />
        </span>
      ),
      title: "Uncompromising Security",
      description: "Bank-level encryption and security measures to protect your financial data.",
      hasLearnMore: true
    },
    {
      icon: (
        <span className="relative inline-block">
          {/* Pink hand-drawn accent: underline */}
          <svg className="absolute left-0 -bottom-2 w-10 h-3" viewBox="0 0 40 12" fill="none">
            <path d="M2 10 Q20 2 38 10" stroke="#EC4899" strokeWidth="2.5" strokeLinecap="round" />
          </svg>
          <Users className="h-10 w-10 text-blue-500 relative z-10" />
        </span>
      ),
      title: "Collaboration",
      description: "Invite your family and friends to join you in your financial journey. Share your insights and collaborate on your financial goals."
    },
    {
      icon: (
        <span className="relative inline-block">
          {/* Pink hand-drawn accent: clock rays */}
          <svg className="absolute -top-3 left-1/2 -translate-x-1/2 w-10 h-6" viewBox="0 0 40 16" fill="none">
            <path d="M20 2v6M8 6l4 2M32 6l-4 2" stroke="#EC4899" strokeWidth="2" strokeLinecap="round" />
          </svg>
          <Calendar className="h-10 w-10 text-blue-500 relative z-10" />
        </span>
      ),
      title: "Budgeting Simplified",
      description: "Weekly, monthly, yearly, and custom budgets. Simply pick a category and number, then track your progress with advanced analytics."
    }
  ];

  return (
    <section ref={sectionRef} id="features" className="py-20 bg-slate-100">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-800">Powerful Features to Drive Smarter Decisions</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card
              key={index}
              className={`bg-white hover:shadow-lg transition-shadow duration-300 overflow-hidden group ${isVisible ? 'animate-fade-in-up' : 'opacity-0'
                }`}
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <CardContent className="p-6">
                <div className="mb-4 p-3 inline-block rounded-lg bg-slate-50 group-hover:bg-slate-100 transition-colors duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-2 text-slate-800">{feature.title}</h3>
                <p className="text-slate-600 mb-4">{feature.description}</p>
                {feature.hasLearnMore && (
                  <a
                    href="/security"
                    className="text-blue-500 hover:text-blue-600 text-sm font-medium inline-flex items-center transition-colors duration-200"
                  >
                    Learn More About Security
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </a>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
        {/* Features Coming Soon Section */}
        <div className="mt-16 text-center">
          {!showComingSoon ? (
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-slate-700">What's Next?</h3>
              <p className="text-slate-600 max-w-2xl mx-auto">
                We're constantly innovating to bring you even more powerful financial tools.
                Curious about what's coming next?
              </p>
              <Button
                onClick={() => setShowComingSoon(true)}
                className="btn-lepton-pink px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                <Eye className="w-4 h-4 mr-2" />
                Peek at What's Coming
                <ChevronDown className="w-4 h-4 ml-2" />
              </Button>
            </div>
          ) : (
            <div className="animate-fade-in-up">
              <h3 className="text-2xl font-bold mb-6 text-slate-700">
                <span className="relative inline-block">
                  Features Coming Soon
                  <span className="absolute bottom-[-3px] left-[-2px] right-[-2px] h-[1.5px] bg-pink-300 rotate-[0.5deg] opacity-100 scale-x-100 origin-left"></span>
                </span>
              </h3>

              {/* Very Soon Section */}
              <div className="mb-8">
                <div className="flex items-center justify-center mb-4">
                  <span className="bg-pink-500 text-white px-4 py-1 rounded-full text-sm font-semibold">Very Soon</span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-6xl mx-auto">
                  <div className="bg-white rounded-lg shadow p-4 border border-dashed border-pink-300 transform hover:scale-105 transition-transform duration-300">
                    <span className="block text-base font-semibold text-pink-600 mb-2">Statement for Download</span>
                    <span className="text-gray-500 text-sm">Export your financial data as statements for easy sharing and record-keeping.</span>
                  </div>
                  <div className="bg-white rounded-lg shadow p-4 border border-dashed border-pink-300 transform hover:scale-105 transition-transform duration-300">
                    <span className="block text-base font-semibold text-pink-600 mb-2">Attach Receipts</span>
                    <span className="text-gray-500 text-sm">Attach receipts to transactions for better record keeping.</span>
                  </div>
                  <div className="bg-white rounded-lg shadow p-4 border border-dashed border-pink-300 transform hover:scale-105 transition-transform duration-300">
                    <span className="block text-base font-semibold text-pink-600 mb-2">Comments & Alerts</span>
                    <span className="text-gray-500 text-sm">Add comments and set alerts to any visual element.</span>
                  </div>
                  <div className="bg-white rounded-lg shadow p-4 border border-dashed border-pink-300 transform hover:scale-105 transition-transform duration-300">
                    <span className="block text-base font-semibold text-pink-600 mb-2">Custom Dashboards</span>
                    <span className="text-gray-500 text-sm">Build your own personalized dashboards.</span>
                  </div>
                </div>
              </div>

              {/* A Little Later Section */}
              <div className="mb-6">
                <div className="flex items-center justify-center mb-4">
                  <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">A Little Later...</span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-6xl mx-auto">
                  <div className="bg-white rounded-lg shadow p-4 border border-dashed border-blue-300 transform hover:scale-105 transition-transform duration-300">
                    <span className="block text-base font-semibold text-blue-600 mb-2">Reports & Invoices</span>
                    <span className="text-gray-500 text-sm">Generate professional reports and invoices for all your needs.</span>
                  </div>
                  <div className="bg-white rounded-lg shadow p-4 border border-dashed border-blue-300 transform hover:scale-105 transition-transform duration-300">
                    <span className="block text-base font-semibold text-blue-600 mb-2">AI Agents</span>
                    <span className="text-gray-500 text-sm">Data analyst agent, reporting agent, and more exciting AI features.</span>
                  </div>
                  <div className="bg-white rounded-lg shadow p-4 border border-dashed border-blue-300 transform hover:scale-105 transition-transform duration-300">
                    <span className="block text-base font-semibold text-blue-600 mb-2">Crypto Accounts</span>
                    <span className="text-gray-500 text-sm">Connect and manage your cryptocurrency accounts.</span>
                  </div>
                  <div className="bg-white rounded-lg shadow p-4 border border-dashed border-blue-300 transform hover:scale-105 transition-transform duration-300">
                    <span className="block text-base font-semibold text-blue-600 mb-2">Mobile Apps</span>
                    <span className="text-gray-500 text-sm">Native mobile applications for iOS and Android.</span>
                  </div>
                </div>
              </div>

              <Button
                onClick={() => setShowComingSoon(false)}
                variant="outline"
                className="mt-6 text-slate-600 hover:text-slate-800"
              >
                Hide Preview
              </Button>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;

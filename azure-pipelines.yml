# Azure DevOps Pipeline for React App -> Azure Static Web Apps
trigger:
  branches:
    include:
      - main

pool:
  vmImage: 'ubuntu-latest'

variables:
  STATIC_WEB_APP_API_TOKEN: $(StaticWebAppApiToken)
  NODE_VERSION: '18.x'

stages:
- stage: Build
  displayName: 'Build React Application'
  jobs:
  - job: BuildJob
    displayName: 'Build'
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: $(NODE_VERSION)
      displayName: 'Install Node.js'
    
    - script: |
        npm ci
      displayName: 'Install dependencies'
      workingDirectory: '$(System.DefaultWorkingDirectory)'
    
    - script: |
        npm run build
      displayName: 'Build React app'
      workingDirectory: '$(System.DefaultWorkingDirectory)'
    
    - publish: $(System.DefaultWorkingDirectory)/dist
      artifact: react-build
      displayName: 'Publish build artifacts'

- stage: Deploy
  displayName: 'Deploy to Azure Static Web Apps'
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - deployment: DeployJob
    displayName: 'Deploy'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - download: current
            artifact: react-build
            displayName: 'Download build artifacts'
          
          - task: AzureStaticWebApp@0
            inputs:
              app_location: 'react-build'
              skip_app_build: true
              azure_static_web_apps_api_token: $(STATIC_WEB_APP_API_TOKEN)
            displayName: 'Deploy to Azure Static Web Apps'
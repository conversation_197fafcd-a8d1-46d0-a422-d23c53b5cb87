
import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from "@/components/ui/button";

const AboutUs = () => {
  return (
    <div className="min-h-screen">
      <Navbar />
      <div className="pt-32 pb-16">
        {/* Hero Section */}
        <div className="container mx-auto px-4 mb-16">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">About FINXPLORER</h1>
            <p className="text-xl text-gray-600 mb-8">
              We're revolutionizing financial management with AI-powered insights and analytics that make complex financial decisions simple.
            </p>
          </div>
        </div>

        {/* Our Mission */}
        <div className="bg-gray-50 py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold mb-8 text-gray-900">Our Mission</h2>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                At FINXPLORER, our mission is to democratize financial intelligence. We believe everyone deserves access to sophisticated financial tools that were previously only available to financial professionals and the wealthy.
              </p>
              <p className="text-xl text-gray-600 leading-relaxed">
                By combining cutting-edge AI technology with an intuitive user experience, we're making financial analysis accessible, understandable, and actionable for individuals and businesses alike.
              </p>
            </div>
          </div>
        </div>

        {/* Our Story */}
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-gray-900">Our Story</h2>
            <p className="text-lg text-gray-600 mb-6">
              FINXPLORER was born from a simple observation: despite living in an age of unprecedented access to information, most people still struggled to make sense of their financial data and make informed financial decisions.
            </p>
            <p className="text-lg text-gray-600 mb-6">
              Founded in 2025 by a team of financial analysts, data scientists, and software engineers, we set out to build a platform that could analyze financial data with the sophistication of a professional analyst but present insights with the clarity and simplicity that everyone could understand.
            </p>
            <p className="text-lg text-gray-600 mb-6">
              Today, FINXPLORER serves thousands of users worldwide, helping them gain deeper insights into their finances, identify opportunities, and achieve their financial goals faster than they thought possible.
            </p>
          </div>
        </div>

        {/* Our Values */}
        <div className="bg-gray-50 py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold mb-12 text-gray-900">Our Values</h2>
              <div className="grid md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-pink-100 text-pink-500 mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-8 h-8">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-gray-800">Transparency</h3>
                  <p className="text-gray-600">
                    We believe in complete transparency with our users, partners, and within our team.
                  </p>
                </div>
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-pink-100 text-pink-500 mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-8 h-8">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-gray-800">Security</h3>
                  <p className="text-gray-600">
                    Your financial data deserves the highest level of protection, which is our top priority.
                  </p>
                </div>
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-pink-100 text-pink-500 mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-8 h-8">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-gray-800">Innovation</h3>
                  <p className="text-gray-600">
                    We constantly push the boundaries of what's possible in financial technology.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Join Us */}
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6 text-gray-900">Join us on our mission</h2>
            <p className="text-xl text-gray-600 mb-8">
              Ready to experience a new way of managing your finances? Join thousands of users who are already benefiting from FINXPLORER's powerful financial insights.
            </p>
            <Button className="bg-pink-500 hover:bg-pink-600 text-lg px-8 py-6">Try the demo</Button>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default AboutUs;


@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 230 60% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 167 68% 46%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
    
    /* Lepton Theme Colors */
    --lepton-pink: 335 100% 65%;
    --lepton-blue: 220 100% 65%;
    --lepton-purple: 265 90% 65%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 230 60% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 167 68% 46%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  .gradient-text {
    @apply bg-gradient-to-r from-purple-500 to-violet-600 bg-clip-text text-transparent;
  }
  
  .gradient-bg {
    @apply bg-gradient-to-r from-purple-600 to-violet-700;
  }
  
  .gradient-bg-subtle {
    @apply bg-gradient-to-r from-purple-50 to-violet-50;
  }
  
  .gradient-border {
    @apply border border-transparent bg-gradient-to-r from-purple-400 to-violet-500 bg-clip-border;
  }
  
  .gradient-card {
    @apply bg-gradient-to-b from-purple-100/40 to-violet-100/20;
  }
  
  .gradient-hero {
    @apply bg-gradient-to-br from-purple-200/40 via-violet-100/30 to-indigo-100/20;
  }
  
  /* Lepton Theme Button Styles */
  .btn-lepton-pink {
    @apply bg-pink-500 hover:bg-pink-600 text-white rounded-md transition-all;
  }
  
  .btn-lepton-blue {
    @apply bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-all;
  }
  
  .btn-lepton-light {
    @apply bg-blue-50 hover:bg-blue-100 text-blue-600 rounded-md transition-all;
  }
  
  .card-lepton {
    @apply bg-white rounded-lg border border-gray-100 shadow-sm hover:shadow-md transition-shadow;
  }
  
  .lepton-section-bg {
    @apply bg-slate-50;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out forwards;
}

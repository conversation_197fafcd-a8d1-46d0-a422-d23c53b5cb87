import React, { useEffect, useRef, useState } from 'react';

const WideCoverageFeature = () => {
  const [visibleItems, setVisibleItems] = useState<boolean[]>([false, false, false]);
  const [underlineVisible, setUnderlineVisible] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  const images = [
    "/images/enrichment1.png",
    "/images/enrichment2.png", 
    "/images/enrichment3.png"
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          // Start animating items when section is visible
          setTimeout(() => {
            setVisibleItems([true, false, false]);
            setUnderlineVisible(true);
            setTimeout(() => setVisibleItems([true, true, false]), 200);
            setTimeout(() => setVisibleItems([true, true, true]), 400);
          }, 300);

          // Unobserve after animation starts
          if (sectionRef.current) {
            observer.unobserve(sectionRef.current);
          }
        }
      },
      { threshold: 0.3 } // Trigger when 30% of the element is visible
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  // Auto-advance carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [images.length]);

  const nextImage = () => {
    setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prevIndex) => (prevIndex - 1 + images.length) % images.length);
  };

  return (
    <section ref={sectionRef} className="py-20 lepton-section-bg">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="order-2 lg:order-1">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Wide Coverage and AI enrichment</h2>
            <p className="text-lg text-gray-600 mb-6">
              Connect to all your financial Institutions from <span className="relative">
                <span>Plaid, MX and Akoya</span>
                <span className={`absolute bottom-[-3px] left-[-2px] right-[-2px] h-[1.5px] bg-pink-300 rotate-[0.5deg] transition-all duration-500 ${underlineVisible ? 'opacity-100 scale-x-100 origin-left' : 'opacity-0 scale-x-0'
                  }`}></span>
                <span className={`absolute bottom-[-5.5px] left-[-1px] right-[-3px] h-[1.5px] bg-pink-300 rotate-[1deg] transition-all duration-500 delay-100 ${underlineVisible ? 'opacity-100 scale-x-100 origin-left' : 'opacity-0 scale-x-0'
                  }`}></span>
              </span>, ensuring comprehensive coverage of your entire financial landscape.
            </p>
            <ul className="space-y-4">
              {[
                "20,000+ financial institutions supported worldwide",
                "Request historical data going back 24 months",
                "Data enriched with machine learning for better categorization and description"
              ].map((text, index) => (
                <li key={index} className="flex items-start">
                  <div className={`flex-shrink-0 h-6 w-6 rounded-full bg-purple-500 flex items-center justify-center mr-3 mt-1 transition-all duration-300 ${visibleItems[index]
                      ? "opacity-100 transform scale-100"
                      : "opacity-0 transform scale-0"
                    }`}>
                    <svg
                      className={`h-4 w-4 text-white transition-all duration-300 ${visibleItems[index] ? "opacity-100" : "opacity-0"
                        }`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <span className="text-gray-700">{text}</span>
                </li>
              ))}
            </ul>
          </div>
          <div className="order-1 lg:order-2">
            <div className="relative">
              <img
                src={images[currentImageIndex]}
                alt="Financial Institutions Coverage"
                className="w-full h-auto object-contain transition-all duration-500 shadow-2xl rounded-lg hover:scale-105 cursor-pointer"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WideCoverageFeature;

import React, { useEffect, useState } from 'react';
import { Button } from "@/components/ui/button";
import { ArrowDown, ExternalLink } from "lucide-react";

const HeroSection = () => {
  const [scrollY, setScrollY] = useState(0);

  // Track scroll position for parallax effect
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Calculate rotation based on scroll position
  const calculateRotation = () => {
    // Start with 15 degrees and reduce based on scroll position
    // We'll go from 15 degrees to 0 degrees within the first 400px of scrolling
    const maxRotation = 15;
    const scrollThreshold = 400;
    const rotation = Math.max(0, maxRotation - (scrollY / scrollThreshold) * maxRotation);
    return rotation;
  };

  const handleScrollDown = () => {
    const featuresSection = document.getElementById('features');
    if (featuresSection) {
      featuresSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="relative min-h-screen flex flex-col justify-center items-center pt-16 pb-8 px-4 bg-gray-900 text-white">
      {/* Background Elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute top-40 -left-36 w-72 h-72 bg-blue-500/10 rounded-full mix-blend-overlay filter blur-3xl opacity-30 animate-pulse-slow"></div>
        <div className="absolute top-1/3 -right-36 w-72 h-72 bg-pink-500/10 rounded-full mix-blend-overlay filter blur-3xl opacity-30 animate-pulse-slow" style={{ animationDelay: "1s" }}></div>
        <div className="absolute bottom-0 left-1/4 w-72 h-72 bg-purple-500/10 rounded-full mix-blend-overlay filter blur-3xl opacity-30 animate-pulse-slow" style={{ animationDelay: "2s" }}></div>
      </div>

      <div className="container relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16 flex flex-col items-center text-center">
        <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold leading-tight mb-6 max-w-4xl text-white">
          Transform Your <span className="text-pink-500">Financial Future</span>
        </h1>

        <div className="text-lg sm:text-xl md:text-2xl text-gray-300 mb-10 max-w-3xl">
          <p className="mb-4">
            For those who don't just want answers - you want insights
          </p>

        </div>

        <div className="flex flex-col sm:flex-row gap-4 mb-16">
          <a href="https://app.finxplorer.com/Account/Login" target="_blank" rel="noopener noreferrer">
            <Button className="btn-lepton-pink px-8 py-6 text-lg">
              Try the demo
            </Button>
          </a>
        </div>

        <div
          className="max-w-5xl w-full animate-float perspective-container"
          style={{
            perspective: "1000px",
            transformStyle: "preserve-3d"
          }}
        >
          <div
            className="rounded-2xl shadow-xl overflow-hidden w-full border border-gray-700"
            style={{
              transform: `rotateX(${calculateRotation()}deg)`,
              transition: "transform 0.1s ease-out"
            }}
          >
            <img
              src="/images/herov2.jpeg"
              alt="Financial Dashboard Preview"
              className="w-full h-full object-cover rounded-lg"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;

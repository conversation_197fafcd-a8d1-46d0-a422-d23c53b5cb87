import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

const PricingSection = () => {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("monthly");

  const plan = {
    name: "All-Inclusive",
    monthlyPrice: "$4.99",
    monthlyOriginalPrice: "$9.99",
    yearlyPrice: "$49.99",
    yearlyOriginalPrice: "$99.00",
    description: "One Plan, Limitless Potential. Unlock the full power of <PERSON>xplore<PERSON> with our all-inclusive plan, designed to transform your financial management.",
    buttonText: "Try the demo",
    buttonClass: "btn-lepton-pink"
  };

  return (
    <section id="pricing" className="py-20 bg-gray-900 text-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Simple Pricing</h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto mb-6">
            Everything you need for your financial journey in one simple plan.
          </p>
          {/* Discount Badge */}
          <div className="inline-flex items-center bg-sky-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            50% OFF - Limited Time Welcome Offer
          </div>
        </div>

        <div className="max-w-md mx-auto mb-8">
          <Tabs
            defaultValue="monthly"
            className="w-full"
            onValueChange={(value) => setBillingCycle(value as "monthly" | "yearly")}
          >
            <TabsList className="grid w-full grid-cols-2 bg-gray-800">
              <TabsTrigger value="monthly">Monthly</TabsTrigger>
              <TabsTrigger value="yearly">Yearly</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <div className="max-w-md mx-auto">
          <Card className="card-lepton relative shadow-xl border-pink-500">
            {/* Welcome Offer Banner */}


            <CardHeader className="pb-0 pt-8">
              <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="mb-6">
                <div className="flex items-center justify-center flex-col">
                  <div className="text-center">
                    <span className="text-3xl font-handwritten text-blue-500 transform -rotate-2 inline-block">
                      {billingCycle === "monthly" ? plan.monthlyPrice : plan.yearlyPrice}
                    </span>
                  </div>
                  <div className="text-center">
                    <span className="text-pink-400 text-4xl font-bold line-through">
                      {billingCycle === "monthly" ? plan.monthlyOriginalPrice : plan.yearlyOriginalPrice}
                    </span>
                    <span className="text-gray-500 ml-2 text-lg">
                      {billingCycle === "monthly" ? "/month" : "/year"}
                    </span>
                  </div>
                </div>
                {billingCycle === "yearly" && (
                  <div className="mt-2 text-sm text-pink-400 font-medium">
                    Save even more with annual billing
                  </div>
                )}
              </div>

              <Button
                className="w-full btn-lepton-pink"
              >
                {plan.buttonText}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;

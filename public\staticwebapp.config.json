{"navigationFallback": {"rewrite": "/index.html", "exclude": ["/assets/*", "/static/*", "*.{css,js,png,jpg,gif,svg,ico,json,txt}"]}, "responseOverrides": {"404": {"rewrite": "/index.html", "statusCode": 200}}, "mimeTypes": {".json": "application/json", ".webmanifest": "application/manifest+json"}, "routes": [{"route": "/*", "allowedRoles": ["anonymous"]}], "forwardingGateway": {"allowedForwardedHosts": ["finxplorer.com"]}}
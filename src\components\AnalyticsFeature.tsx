import React, { useEffect, useRef, useState } from 'react';


const AnalyticsFeature = () => {
  const [visibleItems, setVisibleItems] = useState<boolean[]>([false, false, false]);
  const [underlineVisible, setUnderlineVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          // Start animating items when section is visible
          setTimeout(() => {
            setVisibleItems([true, false, false]);
            setUnderlineVisible(true);
            setTimeout(() => setVisibleItems([true, true, false]), 200);
            setTimeout(() => setVisibleItems([true, true, true]), 400);
          }, 300);

          // Unobserve after animation starts
          if (sectionRef.current) {
            observer.unobserve(sectionRef.current);
          }
        }
      },
      { threshold: 0.3 } // Trigger when 30% of the element is visible
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <section ref={sectionRef} className="py-20 lepton-section-bg" id="whychoose" >


      <div className="container mx-auto px-4">
        <div className="text-center relative">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 max-w-4xl mx-auto inline-block relative z-10 text-gray-800">
            Experience unparalleled financial insights with our powerful platform
          </h2>
        </div>
      </div>

      <div className="container mx-auto px-4 mt-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="order-2 lg:order-1">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Interactive Analytics</h2>
            <p className="text-lg text-gray-600 mb-6">
              Harness the power of <span className="relative">
                <span>interactive</span>
                <span className={`absolute bottom-[-3px] left-[-2px] right-[-2px] h-[1.5px] bg-pink-300 rotate-[0.5deg] transition-all duration-500 ${underlineVisible ? 'opacity-100 scale-x-100 origin-left' : 'opacity-0 scale-x-0'
                  }`}></span>
                <span className={`absolute bottom-[-5.5px] left-[-1px] right-[-3px] h-[1.5px] bg-pink-300 rotate-[1deg] transition-all duration-500 delay-100 ${underlineVisible ? 'opacity-100 scale-x-100 origin-left' : 'opacity-0 scale-x-0'
                  }`}></span>
              </span>, <span className="relative">
                <span>enterprise-class analytics</span>
                <span className={`absolute bottom-[-3px] left-[-2px] right-[-2px] h-[1.5px] bg-pink-300 rotate-[0.5deg] transition-all duration-500 delay-200 ${underlineVisible ? 'opacity-100 scale-x-100 origin-left' : 'opacity-0 scale-x-0'
                  }`}></span>
                <span className={`absolute bottom-[-5.5px] left-[-1px] right-[-3px] h-[1.5px] bg-pink-300 rotate-[1deg] transition-all duration-500 delay-300 ${underlineVisible ? 'opacity-100 scale-x-100 origin-left' : 'opacity-0 scale-x-0'
                  }`}></span>
              </span> and <span className="relative">
                <span>AI</span>
                <span className={`absolute bottom-[-3px] left-[-2px] right-[-2px] h-[1.5px] bg-pink-300 rotate-[0.5deg] transition-all duration-500 delay-400 ${underlineVisible ? 'opacity-100 scale-x-100 origin-left' : 'opacity-0 scale-x-0'
                  }`}></span>
                <span className={`absolute bottom-[-5.5px] left-[-1px] right-[-3px] h-[1.5px] bg-pink-300 rotate-[1deg] transition-all duration-500 delay-500 ${underlineVisible ? 'opacity-100 scale-x-100 origin-left' : 'opacity-0 scale-x-0'
                  }`}></span>
              </span> to uncover deep, meaningful patterns in your financial data, empowering you with actionable insights to optimize your money habits and drive financial success.  
            </p>
            <ul className="space-y-4">
              {[
                "Click on any data point to instantly drill down access granular details and export your findings if needed",
                "Select elements in one chart and watch all related visualizations automatically update in real-time, creating a seamless exploratory experience",
                "Engaging interconnected dashboard that helps you uncover hidden insights and truly understand what's driving your finances"
              ].map((text, index) => {
                const firstSpaceIndex = text.indexOf(" ");
                const firstWord = text.substring(0, firstSpaceIndex);
                const restOfTheText = text.substring(firstSpaceIndex);
                return (
                  <li key={index} className="flex items-start">
                    <div className={`flex-shrink-0 h-6 w-6 rounded-full bg-pink-500 flex items-center justify-center mr-3 mt-1 transition-all duration-300 ${visibleItems[index]
                      ? "opacity-100 transform scale-100"
                      : "opacity-0 transform scale-0"
                      }`}>
                      <svg
                        className={`h-4 w-4 text-white transition-all duration-300 ${visibleItems[index] ? "opacity-100" : "opacity-0"
                          }`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                    <span className="text-gray-700">
                      <strong>{firstWord}</strong>{restOfTheText}
                    </span>
                  </li>
                );
              })}
            </ul>
          </div>
          <div className="order-1 lg:order-2">
            <img
              src="/images/analyticsvideo.gif"
              alt="Analytics Dashboard"
              className="w-full h-auto object-cover shadow-2xl rounded-lg transition-transform duration-300 hover:scale-150 cursor-pointer"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default AnalyticsFeature;


import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

const Company = () => {
  return (
    <div className="min-h-screen">
      <Navbar />
      <div className="pt-32 pb-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">FINXPLORER Company</h1>
            <p className="text-xl text-gray-600">
              We're building the next generation of financial management tools powered by artificial intelligence and data science.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 mb-24">
            <div className="flex flex-col justify-center">
              <h2 className="text-3xl font-bold mb-6 text-gray-900">Our Company</h2>
              <p className="text-lg text-gray-600 mb-6">
                Founded in 2022, FINXPLORER is a rapidly growing fintech company dedicated to making sophisticated financial analysis accessible to everyone.
              </p>
              <p className="text-lg text-gray-600 mb-6">
                Our platform combines cutting-edge AI technology with intuitive design to provide users with powerful insights into their financial data, helping them make better financial decisions.
              </p>
              <div className="mt-6">
                <Button asChild className="bg-pink-500 hover:bg-pink-600">
                  <Link to="/about-us">Learn About Us</Link>
                </Button>
              </div>
            </div>
            <div className="bg-gray-200 h-80 rounded-lg"></div>
          </div>
          
          <div className="mb-24">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-6 text-gray-900">Company Resources</h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Everything you need to know about FINXPLORER and how we're changing the future of personal finance.
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8">
              <Link to="/about-us" className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-3 text-gray-800">About Us</h3>
                  <p className="text-gray-600 mb-4">
                    Learn about our mission, values, and the team behind FINXPLORER.
                  </p>
                  <span className="text-pink-500 font-medium">Read more →</span>
                </div>
              </Link>
              
              <Link to="/privacy-policy" className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-3 text-gray-800">Privacy Policy</h3>
                  <p className="text-gray-600 mb-4">
                    Details on how we collect, use, and protect your personal information.
                  </p>
                  <span className="text-pink-500 font-medium">Read more →</span>
                </div>
              </Link>
              
              <Link to="/terms-of-service" className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-3 text-gray-800">Terms of Service</h3>
                  <p className="text-gray-600 mb-4">
                    The rules and guidelines for using our platform and services.
                  </p>
                  <span className="text-pink-500 font-medium">Read more →</span>
                </div>
              </Link>
            </div>
          </div>
          
          <div className="bg-gray-50 rounded-2xl p-12 text-center">
            <h2 className="text-3xl font-bold mb-6 text-gray-900">Ready to experience FINXPLORER?</h2>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Join thousands of users who are already making smarter financial decisions with our platform.
            </p>
            <Button className="bg-pink-500 hover:bg-pink-600 text-lg px-8 py-6">Get Started Today</Button>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Company;

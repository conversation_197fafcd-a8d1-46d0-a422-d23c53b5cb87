
import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const SupportCenter = () => {
  return (
    <div className="min-h-screen">
      <Navbar />
      <div className="pt-32 pb-16 container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4 text-gray-900">Contact Us</h1>
            <p className="text-lg text-gray-600">
              Have a question or need assistance? We'd love to hear from you.
            </p>
          </div>

          <div className="bg-white p-8 rounded-lg border border-gray-200 shadow-sm">
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                  <Input id="name" placeholder="Your name" className="h-12" />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <Input id="email" type="email" placeholder="<EMAIL>" className="h-12" />
                </div>
              </div>
              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                <Input id="subject" placeholder="How can we help?" className="h-12" />
              </div>
              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">Message</label>
                <textarea
                  id="message"
                  rows={6}
                  className="w-full border border-gray-300 rounded-md shadow-sm p-3 focus:ring-pink-500 focus:border-pink-500 resize-none"
                  placeholder="Please describe your question or issue in detail..."
                ></textarea>
              </div>
              <Button
                type="submit"
                className="w-full bg-pink-500 hover:bg-pink-600 h-12 text-lg"
              >
                Send Message
              </Button>
            </form>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default SupportCenter;

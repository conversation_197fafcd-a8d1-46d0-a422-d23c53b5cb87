import React from 'react';

const WhyChooseSection = () => {
  return (
    <section className="py-16 bg-white" >
      <div className="container mx-auto px-4">
        <div className="text-center relative">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 max-w-4xl mx-auto inline-block relative z-10 font-handwritten text-gray-800">
            For those who don't just want summaries - they want the {' '}
            <span className="relative inline-block">
              story
              <span className="absolute bottom-[-3px] left-[-2px] right-[-2px] h-[1.5px] bg-pink-300 rotate-[0.5deg] opacity-100 scale-x-100 origin-left transition-all duration-500"></span>
              <span className="absolute bottom-[-5.5px] left-[-1px] right-[-3px] h-[1.5px] bg-pink-300 rotate-[1deg] opacity-100 scale-x-100 origin-left transition-all duration-500 delay-100"></span>
            </span>
          </h2>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseSection;


import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from "@/components/ui/button";
import { <PERSON>, Sparkles, Clock } from 'lucide-react';

const Blog = () => {
  return (
    <div className="min-h-screen">
      <Navbar />

      {/* Coming Soon Section with Dark Background */}
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
          <div className="absolute top-40 right-20 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
          <div className="absolute -bottom-8 left-40 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-pink-400 rounded-full animate-float opacity-60"></div>
          <div className="absolute top-3/4 left-3/4 w-1 h-1 bg-blue-400 rounded-full animate-float opacity-40" style={{ animationDelay: '1s' }}></div>
          <div className="absolute top-1/2 left-1/6 w-1.5 h-1.5 bg-purple-400 rounded-full animate-float opacity-50" style={{ animationDelay: '2s' }}></div>
          <div className="absolute top-1/6 right-1/4 w-1 h-1 bg-pink-300 rounded-full animate-float opacity-30" style={{ animationDelay: '3s' }}></div>
        </div>

        {/* Main Content */}
        <div className="relative z-10 text-center px-4 max-w-4xl mx-auto">
          {/* Icon with Glow Effect */}
          <div className="mb-8 relative">
            <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full shadow-2xl">
              <Sparkles className="w-12 h-12 text-white animate-pulse" />
            </div>
            <div className="absolute inset-0 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full blur-xl opacity-30 animate-pulse"></div>
          </div>

          {/* Coming Soon Text */}
          <h1 className="text-6xl md:text-8xl font-bold text-white mb-6 tracking-tight">
            <span className="bg-gradient-to-r from-pink-400 via-purple-400 to-blue-400 bg-clip-text text-transparent animate-pulse">
              Coming Soon
            </span>
          </h1>

          {/* Subtitle */}
          <h2 className="text-2xl md:text-3xl font-semibold text-gray-300 mb-4">
            FINXPLORER Blog
          </h2>

          {/* Description */}
          <p className="text-lg md:text-xl text-gray-400 mb-8 max-w-2xl mx-auto leading-relaxed">
            We're crafting amazing content about financial insights, AI-powered analytics, and money management tips.
            Get ready for expert advice that will transform your financial journey.
          </p>

          {/* Features Preview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-3xl mx-auto">
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-6 border border-white border-opacity-20">
              <Clock className="w-8 h-8 text-pink-400 mx-auto mb-3" />
              <h3 className="text-white font-semibold mb-2">Weekly Insights</h3>
              <p className="text-gray-300 text-sm">Fresh financial tips and market analysis every week</p>
            </div>
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-6 border border-white border-opacity-20">
              <Sparkles className="w-8 h-8 text-purple-400 mx-auto mb-3" />
              <h3 className="text-white font-semibold mb-2">AI & Finance</h3>
              <p className="text-gray-300 text-sm">Deep dives into AI-powered financial tools and strategies</p>
            </div>
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-6 border border-white border-opacity-20">
              <Bell className="w-8 h-8 text-blue-400 mx-auto mb-3" />
              <h3 className="text-white font-semibold mb-2">Expert Advice</h3>
              <p className="text-gray-300 text-sm">Professional insights from financial experts and analysts</p>
            </div>
          </div>




        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Blog;

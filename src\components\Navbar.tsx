import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Menu } from "lucide-react";
import FxIcon from './FxIcon';
import { useLocation } from 'react-router-dom';

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSignUpOpen, setIsSignUpOpen] = useState(false);
  const [email, setEmail] = useState('');
  const location = useLocation();
  const isHomePage = location.pathname === '/';

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const textColorClass = isHomePage ? (isScrolled ? 'text-gray-700' : 'text-white') : 'text-gray-700';
  const bgClass = isHomePage
    ? (isScrolled ? 'bg-white bg-opacity-90 shadow-md' : 'bg-transparent shadow-none')
    : 'bg-white shadow-md';

  const handleNavClick = (event: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
    event.preventDefault();
    setIsMobileMenuOpen(false);

    const sectionId = targetId.substring(1);
    const section = document.getElementById(sectionId);

    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
    } else {
      console.warn(`[Navbar] Section with id "${sectionId}" not found. Ensure the target element has this ID.`);
    }
  };

  const handleSignUpSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    if (email.trim()) {
      // Here you would typically send the email to your backend/API
      console.log('Email captured:', email);
      alert(`Thank you for signing up! We'll be in touch at ${email}`);
      setEmail('');
      setIsSignUpOpen(false);
    }
  };

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 backdrop-blur-sm ${bgClass}`}>
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <a href="/" className="flex items-center space-x-3">
              <FxIcon className="h-10 w-10" />
              <span className={`text-2xl font-bold uppercase ${textColorClass}`}>FINXPLORER</span>
            </a>
          </div>

          <div className="hidden md:flex items-center space-x-6">
            <nav className="flex items-center space-x-6">
              <a href="#features" onClick={(e) => handleNavClick(e, '#features')} className={`text-sm font-medium ${textColorClass} hover:text-pink-500 transition-colors`}>Features</a>
              <a href="#pricing" onClick={(e) => handleNavClick(e, '#pricing')} className={`text-sm font-medium ${textColorClass} hover:text-pink-500 transition-colors`}>Pricing</a>
              <Dialog open={isSignUpOpen} onOpenChange={setIsSignUpOpen}>
                <DialogTrigger asChild>
                  <Button variant="default" className="bg-pink-500 hover:bg-pink-600 text-white">
                    Sign Up
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Join Our Waitlist</DialogTitle>
                  </DialogHeader>
                  <form onSubmit={handleSignUpSubmit} className="space-y-4">
                    <div>
                      <Input
                        type="email"
                        placeholder="Enter your email address"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        className="w-full"
                      />
                    </div>
                    <Button type="submit" className="w-full bg-pink-500 hover:bg-pink-600">
                      Submit
                    </Button>
                  </form>
                </DialogContent>
              </Dialog>
            </nav>
          </div>

          <div className="md:hidden">
            <Button variant="ghost" size="icon" onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}>
              <Menu className={`h-6 w-6 ${textColorClass}`} />
            </Button>
          </div>
        </div>

        {isMobileMenuOpen && (
          <div className="md:hidden bg-white rounded-lg mt-2 p-4 shadow-lg animate-fade-in-up">
            <nav className="flex flex-col space-y-3">
              <a href="#features" onClick={(e) => handleNavClick(e, '#features')} className="text-sm font-medium text-gray-700 hover:text-pink-500 py-2 transition-colors">Features</a>
              <a href="#pricing" onClick={(e) => handleNavClick(e, '#pricing')} className="text-sm font-medium text-gray-700 hover:text-pink-500 py-2 transition-colors">Pricing</a>
              <Dialog open={isSignUpOpen} onOpenChange={setIsSignUpOpen}>
                <DialogTrigger asChild>
                  <Button variant="default" className="bg-pink-500 hover:bg-pink-600 text-white w-full mt-2">
                    Sign Up
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Join Our Waitlist</DialogTitle>
                  </DialogHeader>
                  <form onSubmit={handleSignUpSubmit} className="space-y-4">
                    <div>
                      <Input
                        type="email"
                        placeholder="Enter your email address"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        className="w-full"
                      />
                    </div>
                    <Button type="submit" className="w-full bg-pink-500 hover:bg-pink-600">
                      Submit
                    </Button>
                  </form>
                </DialogContent>
              </Dialog>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Navbar;

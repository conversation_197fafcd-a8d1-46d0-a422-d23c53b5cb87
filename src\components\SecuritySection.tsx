
import React, { useEffect, useRef, useState } from 'react';
import { Shield } from "lucide-react";
import { Button } from "@/components/ui/button";

const SecuritySection = () => {
  const [visibleItems, setVisibleItems] = useState<boolean[]>([false, false, false]);
  const sectionRef = useRef<HTMLElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          // Start animating items when section is visible
          setTimeout(() => {
            setVisibleItems([true, false, false]);
            setTimeout(() => setVisibleItems([true, true, false]), 300);
            setTimeout(() => setVisibleItems([true, true, true]), 600);
          }, 400);
          
          // Unobserve after animation starts
          if (sectionRef.current) {
            observer.unobserve(sectionRef.current);
          }
        }
      },
      { threshold: 0.3 } // Trigger when 30% of the element is visible
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <section ref={sectionRef} id="security" className="py-20 bg-gray-900 text-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">No Advertising, Transparent Privacy and Bank-Level Security</h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto">
            We Run the Most Powerful Investment Financial Tracker and Budgeting Tool Including Advanced Analysis and Reporting
          </p>
        </div>

        <div className="flex justify-center">
          <div className="bg-gray-800 rounded-xl p-8 max-w-3xl">
            <div className="mb-6 p-4 inline-block rounded-full bg-pink-500/20">
              <Shield className="h-10 w-10 text-pink-500" />
            </div>
            <h3 className="text-2xl font-semibold mb-4">Uncompromising Security</h3>
            <p className="text-gray-300 mb-6">
              Rest easy knowing your financial data is safeguarded with bank-level, state-of-the-art encryption and security measures, trusted by millions to protect their financial future.
            </p>
            <ul className="space-y-3 text-gray-300">
              {[
                "Hosted on MS Azure with TDE, firewalls, dynamic data masking",
                "AI processing - only data schema is sent, not your financial details",
                "End-to-end encryption for all your sensitive data"
              ].map((text, index) => (
                <li key={index} className="flex items-start">
                  <div className={`flex-shrink-0 h-5 w-5 rounded-full bg-pink-500 flex items-center justify-center mr-3 mt-1 transition-all duration-300 ${
                    visibleItems[index] 
                      ? "opacity-100 transform scale-100" 
                      : "opacity-0 transform scale-0"
                  }`}>
                    <svg 
                      className={`h-3 w-3 text-white transition-all duration-300 ${
                        visibleItems[index] ? "opacity-100" : "opacity-0"
                      }`} 
                      fill="none" 
                      viewBox="0 0 24 24" 
                      stroke="currentColor"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M5 13l4 4L19 7" 
                      />
                    </svg>
                  </div>
                  <span>{text}</span>
                </li>
              ))}
            </ul>
            <div className="mt-8">
              <Button className="btn-lepton-pink px-6 py-2">Learn More</Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SecuritySection;
